2025-08-05 16:50:38.748 - chat_with_robot - chat_with_robot.py - <module> - line 921 - INFO - use_action: dont
2025-08-05 16:50:38.748 - chat_with_robot - chat_with_robot.py - <module> - line 922 - INFO - 
[启动HardwareAIAgent交互程序]

2025-08-05 16:50:38.754 - chat_with_robot - chat_with_robot.py - init_websocket - line 533 - INFO - url: wss://joyinside.jd.com/soulmate/voiceCall/v4?accessVersion=V2&accessTimestamp=1754383838754&accessNonce=53b62451-7b47-4b4c-aa71-dbcb196a24fd&accessKeyId=cfa57b9ed4d740dd9128a211&botId=da6fecedd53343bc8d96fe04a000c41d&sessionId=42975dcc-71d9-11f0-8035-dc4546c07870&requestId=9b095e8e-cb15-4688-8360-4fc180dd5127_joyinside&accessSign=fe0db72ba1082ab3bf0969ed69e41b37, request_id: 9b095e8e-cb15-4688-8360-4fc180dd5127_joyinside
2025-08-05 16:50:38.755 - chat_with_robot - websocket_client_thread.py - _cleanup - line 405 - INFO - 开始清理旧连接...
2025-08-05 16:50:38.755 - chat_with_robot - websocket_client_thread.py - _cleanup - line 427 - INFO - 清理完成
2025-08-05 16:50:39.116 - chat_with_robot - websocket_client_thread.py - _on_open - line 331 - INFO - WebSocket连接建立
2025-08-05 16:50:39.260 - chat_with_robot - audio_player.py - start - line 53 - INFO - 音频播放线程已启动
